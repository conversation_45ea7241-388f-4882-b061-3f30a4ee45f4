const axios = require('axios');

const ROUTER_CONFIG = {
  ip: '*************',
  password: 'pcs2ass2ADM'
};

async function testRealApis() {
  console.log('=== Testing Real API Endpoints ===\n');
  
  try {
    // Step 1: Login and get session
    console.log('Step 1: Login');
    const loginRes = await axios.post(`http://${ROUTER_CONFIG.ip}/cgi-bin/luci/api/auth`, {
      method: "login",
      params: {
        password: ROUTER_CONFIG.password,
        time: Math.floor(Date.now() / 1000).toString(),
        encry: false,
        limit: false
      }
    }, {
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0',
      }
    });

    if (!loginRes.data?.data?.token) {
      console.log('❌ Login failed');
      return;
    }

    const token = loginRes.data.data.token;
    
    // Extract session cookie
    let sessionCookie = '';
    if (loginRes.headers['set-cookie']) {
      const cookies = loginRes.headers['set-cookie'];
      for (const cookie of cookies) {
        const [nameValue] = cookie.split(';');
        sessionCookie = nameValue;
        break;
      }
    }

    console.log(`✓ Login successful - Token: ${token.substring(0, 8)}...`);

    // Step 2: Test the real API endpoints found in JavaScript
    console.log('\nStep 2: Testing real API endpoints with token-based URLs\n');
    
    const realApis = [
      `/cgi-bin/luci/;stok=${token}/api/common`,
      `/cgi-bin/luci/;stok=${token}/api/cmd`,
      `/cgi-bin/luci/;stok=${token}/api/diagnose`,
      `/cgi-bin/luci/;stok=${token}/api/network`,
      `/cgi-bin/luci/;stok=${token}/api/overview`,
      `/cgi-bin/luci/;stok=${token}/api/system`,
      `/cgi-bin/luci/;stok=${token}/api/wireless`,
      `/cgi-bin/luci/;stok=${token}/api/quickset`,
      `/cgi-bin/luci/;stok=${token}/api/advanced`,
      `/cgi-bin/luci/;stok=${token}/api/switch`,
    ];

    for (const apiUrl of realApis) {
      try {
        console.log(`Testing: ${apiUrl}`);
        
        // Try GET request first
        const getResponse = await axios.get(`http://${ROUTER_CONFIG.ip}${apiUrl}`, {
          headers: {
            'User-Agent': 'Mozilla/5.0',
            'Cookie': sessionCookie,
            'Accept': 'application/json, text/plain, */*',
            'X-Requested-With': 'XMLHttpRequest'
          },
          timeout: 5000
        });

        console.log(`  GET Status: ${getResponse.status}`);
        console.log(`  Content-Type: ${getResponse.headers['content-type'] || 'unknown'}`);
        
        if (getResponse.headers['content-type']?.includes('json') || 
            (typeof getResponse.data === 'object' && getResponse.data !== null)) {
          console.log(`  🎯 JSON Response: ${JSON.stringify(getResponse.data).substring(0, 300)}...`);
          
          // Check for device data
          const dataStr = JSON.stringify(getResponse.data).toLowerCase();
          if (dataStr.includes('mac') || dataStr.includes('ip') || dataStr.includes('client') || 
              dataStr.includes('device') || dataStr.includes('dhcp') || dataStr.includes('192.168')) {
            console.log(`  🔥 CONTAINS DEVICE DATA!`);
          }
        } else if (getResponse.data && getResponse.data.length < 500) {
          console.log(`  📄 Text Response: ${getResponse.data.substring(0, 200)}...`);
        }
        
        console.log('');
        
      } catch (error) {
        if (error.response) {
          console.log(`  ❌ GET failed - Status: ${error.response.status}`);
        } else {
          console.log(`  ❌ GET failed - Error: ${error.message}`);
        }
      }
    }

    // Step 3: Test POST requests to these APIs with common method patterns
    console.log('\nStep 3: Testing POST requests with method patterns\n');
    
    const apiMethods = [
      // Network API methods
      { api: 'network', method: 'get' },
      { api: 'network', method: 'status' },
      { api: 'network', method: 'clients' },
      { api: 'network', method: 'dhcp' },
      { api: 'network', method: 'devices' },
      { api: 'network', method: 'list' },
      
      // Overview API methods  
      { api: 'overview', method: 'get' },
      { api: 'overview', method: 'status' },
      { api: 'overview', method: 'data' },
      { api: 'overview', method: 'info' },
      
      // Wireless API methods
      { api: 'wireless', method: 'get' },
      { api: 'wireless', method: 'status' },
      { api: 'wireless', method: 'clients' },
      { api: 'wireless', method: 'devices' },
      { api: 'wireless', method: 'list' },
      
      // System API methods
      { api: 'system', method: 'get' },
      { api: 'system', method: 'status' },
      { api: 'system', method: 'info' },
      
      // Common API methods
      { api: 'common', method: 'get' },
      { api: 'common', method: 'status' },
      { api: 'common', method: 'data' },
    ];

    for (const { api, method } of apiMethods) {
      try {
        const apiUrl = `/cgi-bin/luci/;stok=${token}/api/${api}`;
        const postData = { method: method };
        
        const response = await axios.post(`http://${ROUTER_CONFIG.ip}${apiUrl}`, postData, {
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0',
            'Cookie': sessionCookie,
            'Accept': 'application/json, text/plain, */*',
            'X-Requested-With': 'XMLHttpRequest'
          },
          timeout: 5000
        });

        console.log(`✓ POST ${api}/${method}`);
        console.log(`  Status: ${response.status}`);
        
        if (response.headers['content-type']?.includes('json') || 
            (typeof response.data === 'object' && response.data !== null)) {
          console.log(`  🎯 JSON Response: ${JSON.stringify(response.data).substring(0, 300)}...`);
          
          // Check for device data
          const dataStr = JSON.stringify(response.data).toLowerCase();
          if (dataStr.includes('mac') || dataStr.includes('ip') || dataStr.includes('client') || 
              dataStr.includes('device') || dataStr.includes('dhcp') || dataStr.includes('192.168')) {
            console.log(`  🔥 CONTAINS DEVICE DATA!`);
          }
          
          // Check for successful response (not "Method not found")
          if (response.data && response.data.error && response.data.error.message === "Method not found.") {
            // Skip logging for method not found
          } else if (response.data && response.data.data !== null) {
            console.log(`  ✅ SUCCESS - Got real data!`);
          }
        }
        console.log('');
        
      } catch (error) {
        if (error.response && error.response.status !== 404) {
          console.log(`❌ POST ${api}/${method} - Status: ${error.response.status}`);
        }
      }
    }

    // Step 4: Try some alternative API patterns
    console.log('\nStep 4: Testing alternative API patterns\n');
    
    // Try without method parameter (direct endpoint calls)
    const directApis = [
      `/cgi-bin/luci/;stok=${token}/api/network/status`,
      `/cgi-bin/luci/;stok=${token}/api/network/clients`,
      `/cgi-bin/luci/;stok=${token}/api/network/dhcp`,
      `/cgi-bin/luci/;stok=${token}/api/overview/status`,
      `/cgi-bin/luci/;stok=${token}/api/overview/data`,
      `/cgi-bin/luci/;stok=${token}/api/wireless/status`,
      `/cgi-bin/luci/;stok=${token}/api/wireless/clients`,
      `/cgi-bin/luci/;stok=${token}/api/system/status`,
      `/cgi-bin/luci/;stok=${token}/api/system/info`,
    ];

    for (const apiUrl of directApis) {
      try {
        const response = await axios.get(`http://${ROUTER_CONFIG.ip}${apiUrl}`, {
          headers: {
            'User-Agent': 'Mozilla/5.0',
            'Cookie': sessionCookie,
            'Accept': 'application/json, text/plain, */*',
            'X-Requested-With': 'XMLHttpRequest'
          },
          timeout: 5000
        });

        console.log(`✓ ${apiUrl.split('/').slice(-2).join('/')}`);
        console.log(`  Status: ${response.status}`);
        
        if (response.headers['content-type']?.includes('json') || 
            (typeof response.data === 'object' && response.data !== null)) {
          console.log(`  🎯 JSON Response: ${JSON.stringify(response.data).substring(0, 300)}...`);
          
          // Check for device data
          const dataStr = JSON.stringify(response.data).toLowerCase();
          if (dataStr.includes('mac') || dataStr.includes('ip') || dataStr.includes('client') || 
              dataStr.includes('device') || dataStr.includes('dhcp') || dataStr.includes('192.168')) {
            console.log(`  🔥 CONTAINS DEVICE DATA!`);
          }
          
          if (response.data && response.data.data !== null && response.data.data !== undefined) {
            console.log(`  ✅ SUCCESS - Got real data!`);
          }
        }
        console.log('');
        
      } catch (error) {
        if (error.response && error.response.status !== 404) {
          console.log(`❌ ${apiUrl.split('/').slice(-2).join('/')} - Status: ${error.response.status}`);
        }
      }
    }

  } catch (error) {
    console.error('❌ Real API test failed:', error.message);
  }
}

testRealApis().catch(console.error);
