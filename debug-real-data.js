const axios = require('axios');

const ROUTER_CONFIG = {
  ip: '*************',
  password: 'pcs2ass2ADM'
};

async function debugRealData() {
  console.log('=== Debugging Real Device Data ===\n');
  
  try {
    // Step 1: Login and get session
    console.log('Step 1: Login');
    const loginRes = await axios.post(`http://${ROUTER_CONFIG.ip}/cgi-bin/luci/api/auth`, {
      method: "login",
      params: {
        password: ROUTER_CONFIG.password,
        time: Math.floor(Date.now() / 1000).toString(),
        encry: false,
        limit: false
      }
    }, {
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0',
      }
    });

    if (!loginRes.data?.data?.token) {
      console.log('❌ Login failed');
      return;
    }

    const token = loginRes.data.data.token;
    
    // Extract session cookie
    let sessionCookie = '';
    if (loginRes.headers['set-cookie']) {
      const cookies = loginRes.headers['set-cookie'];
      for (const cookie of cookies) {
        const [nameValue] = cookie.split(';');
        sessionCookie = nameValue;
        break;
      }
    }

    console.log(`✓ Login successful - Token: ${token.substring(0, 8)}...`);
    console.log(`✓ Session cookie: ${sessionCookie.split('=')[0]}`);

    // Step 2: Get the actual HTML content from the working page
    console.log('\nStep 2: Fetching real device page content');
    
    const response = await axios.get(`http://${ROUTER_CONFIG.ip}/cgi-bin/luci/;stok=${token}/ehr/home_user`, {
      headers: {
        'User-Agent': 'Mozilla/5.0',
        'Referer': `http://${ROUTER_CONFIG.ip}/cgi-bin/luci/;stok=${token}/web/home`,
        'Cookie': sessionCookie
      }
    });

    console.log(`Response status: ${response.status}`);
    console.log(`Response length: ${response.data.length} bytes`);
    
    if (response.data.length > 15000) {
      console.log('❌ Got login page (large response)');
      return;
    }

    console.log('\n=== FULL HTML CONTENT ===');
    console.log(response.data);
    console.log('\n=== END HTML CONTENT ===');

    // Step 3: Look for specific patterns that might contain device data
    console.log('\nStep 3: Analyzing content for device data patterns');
    
    const html = response.data;
    
    // Check for JavaScript variables
    console.log('\n--- JavaScript Variables ---');
    const jsVarMatches = html.match(/var\s+\w+\s*=\s*[^;]+;/g) || [];
    jsVarMatches.forEach((match, i) => {
      console.log(`JS Var ${i + 1}: ${match}`);
    });

    // Check for JSON-like structures
    console.log('\n--- JSON-like Structures ---');
    const jsonMatches = html.match(/\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}/g) || [];
    jsonMatches.forEach((match, i) => {
      console.log(`JSON ${i + 1}: ${match.substring(0, 200)}${match.length > 200 ? '...' : ''}`);
    });

    // Check for IP addresses
    console.log('\n--- IP Addresses Found ---');
    const ipMatches = html.match(/\d+\.\d+\.\d+\.\d+/g) || [];
    const uniqueIps = [...new Set(ipMatches)];
    uniqueIps.forEach(ip => {
      console.log(`IP: ${ip}`);
    });

    // Check for MAC addresses
    console.log('\n--- MAC Addresses Found ---');
    const macMatches = html.match(/[0-9A-Fa-f]{2}[:-][0-9A-Fa-f]{2}[:-][0-9A-Fa-f]{2}[:-][0-9A-Fa-f]{2}[:-][0-9A-Fa-f]{2}[:-][0-9A-Fa-f]{2}/g) || [];
    const uniqueMacs = [...new Set(macMatches)];
    uniqueMacs.forEach(mac => {
      console.log(`MAC: ${mac}`);
    });

    // Check for tables
    console.log('\n--- HTML Tables ---');
    const tableMatches = html.match(/<table[^>]*>[\s\S]*?<\/table>/gi) || [];
    console.log(`Found ${tableMatches.length} tables`);
    tableMatches.forEach((table, i) => {
      console.log(`Table ${i + 1} preview: ${table.substring(0, 300)}...`);
    });

    // Check for script tags that might load data dynamically
    console.log('\n--- Script Tags ---');
    const scriptMatches = html.match(/<script[^>]*>[\s\S]*?<\/script>/gi) || [];
    console.log(`Found ${scriptMatches.length} script tags`);
    scriptMatches.forEach((script, i) => {
      if (script.includes('ajax') || script.includes('fetch') || script.includes('xhr') || script.includes('client') || script.includes('device')) {
        console.log(`Relevant Script ${i + 1}: ${script.substring(0, 500)}...`);
      }
    });

    // Check for AJAX endpoints or API calls in the HTML
    console.log('\n--- Potential AJAX/API Endpoints ---');
    const ajaxMatches = html.match(/['"`]\/[^'"`\s]*(?:api|ajax|data|client|device|user)[^'"`\s]*['"`]/gi) || [];
    const uniqueEndpoints = [...new Set(ajaxMatches)];
    uniqueEndpoints.forEach(endpoint => {
      console.log(`Endpoint: ${endpoint}`);
    });

  } catch (error) {
    console.error('❌ Debug failed:', error.message);
  }
}

debugRealData().catch(console.error);
