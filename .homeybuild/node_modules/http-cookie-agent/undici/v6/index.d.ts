/* eslint-disable no-redeclare */
import type { <PERSON><PERSON><PERSON><PERSON> } from 'tough-cookie';
import { Agent, Client } from 'undici';

export interface CookieOptions {
  jar: <PERSON><PERSON><PERSON><PERSON>;
}

export namespace CookieAgent {
  export interface Options extends Agent.Options {
    cookies?: CookieOptions | undefined;
  }
}

export class CookieAgent extends Agent {
  constructor(options?: CookieAgent.Options);
}

export namespace CookieClient {
  export interface Options extends Client.Options {
    cookies?: CookieOptions | undefined;
  }
}

export class CookieClient extends Client {
  constructor(url: string | URL, options?: CookieClient.Options);
}

export function createCookieClient<BaseClient extends Client = Client, BaseClientOptions = unknown>(
  BaseClientClass: new (origin: string | URL, options: BaseClientOptions) => BaseClient,
): new (origin: string | URL, options?: BaseClientOptions & { cookies?: CookieOptions | undefined }) => BaseClient;
