{"name": "axios-cookiejar-support", "version": "6.0.2", "description": "Add tough-cookie support to axios.", "keywords": ["axios", "cookie", "cookiejar", "cookies", "tough-cookie"], "homepage": "https://github.com/3846masa/axios-cookiejar-support#readme", "bugs": {"url": "https://github.com/3846masa/axios-cookiejar-support/issues"}, "repository": {"type": "git", "url": "git+https://github.com/3846masa/axios-cookiejar-support.git"}, "funding": "https://github.com/sponsors/3846masa", "license": "MIT", "author": "3846masa <3846mas<PERSON><EMAIL>>", "type": "module", "exports": {".": {"node": {"default": "./dist/index.js", "types": "./dist/index.d.ts"}, "browser": {"default": "./noop.js", "types": "./dist/index.d.ts"}, "default": {"default": "./dist/index.js", "types": "./dist/index.d.ts"}}}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist", "noop.js"], "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsc --project tsconfig.build.json", "format": "pnpm run --sequential \"/^format:.*/\"", "format:eslint": "eslint --fix .", "format:prettier": "prettier --write .", "prelint": "pnpm run build", "lint": "pnpm run \"/^lint:.*/\"", "lint:eslint": "eslint .", "lint:prettier": "prettier --check .", "lint:tsc": "tsc --noEmit", "semantic-release": "semantic-release", "test": "NODE_OPTIONS=\"--experimental-vm-modules\" jest"}, "dependencies": {"http-cookie-agent": "^7.0.1"}, "devDependencies": {"@3846masa/configs": "github:3846masa/configs#e0c70a82447c0b40dfdf556cd94c02bbd2be9a81", "@babel/plugin-proposal-explicit-resource-management": "7.25.9", "@babel/preset-env": "7.26.9", "@babel/preset-typescript": "7.27.0", "@jest/globals": "29.7.0", "@semantic-release/changelog": "6.0.3", "@semantic-release/exec": "7.0.3", "@semantic-release/git": "10.0.1", "@types/eslint": "9.6.1", "@types/node": "20.17.31", "axios": "1.8.4", "babel-jest": "29.7.0", "disposablestack": "1.1.7", "jest": "29.7.0", "rimraf": "6.0.1", "semantic-release": "24.2.3", "tough-cookie": "5.1.2", "typescript": "5.8.3"}, "peerDependencies": {"axios": ">=0.20.0", "tough-cookie": ">=4.0.0"}, "packageManager": "pnpm@10.9.0", "engines": {"node": ">=20.0.0"}, "publishConfig": {"access": "public"}, "pnpm": {"patchedDependencies": {"@semantic-release/git@10.0.1": "patches/@<EMAIL>"}}}