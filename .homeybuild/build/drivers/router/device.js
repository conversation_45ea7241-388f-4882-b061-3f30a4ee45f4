"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const homey_1 = __importDefault(require("homey"));
const RouterApiClient_1 = require("../../lib/RouterApiClient");
class RouterDevice extends homey_1.default.Device {
    constructor() {
        super(...arguments);
        this.client = null;
        this.statusUpdateInterval = null;
        this.statusUpdateIntervalMs = 60000; // 1 minute
    }
    /**
     * onInit is called when the device is initialized.
     */
    async onInit() {
        this.log('Router device has been initialized');
        // Initialize capabilities
        this.log('Registering capabilities');
        await this.migrateCapabilities();
        this.registerCapabilities();
        this.log('Capabilities registered successfully');
        // Register flow cards
        this.log('Registering flow cards');
        this.registerFlowCards();
        this.log('Flow cards registered successfully');
        // Initialize router client
        this.log('Initializing router client');
        try {
            await this.initRouterClient();
            this.log('Router client initialized successfully');
        }
        catch (error) {
            this.error('Failed to initialize router client:', error);
        }
        // Start periodic status updates
        this.log('Starting periodic status updates');
        this.startStatusUpdates();
        this.log('Periodic status updates started');
    }
    /**
     * Migrate capabilities for existing devices
     */
    async migrateCapabilities() {
        try {
            const requiredCapabilities = [
                'onoff',
                'button.restart',
                'guestwifi',
                'measure_uptime',
                'measure_connected_devices',
                'measure_cpu_usage',
                'measure_memory_usage'
            ];
            const currentCapabilities = this.getCapabilities();
            for (const capability of requiredCapabilities) {
                if (!currentCapabilities.includes(capability)) {
                    this.log(`Adding missing capability: ${capability}`);
                    await this.addCapability(capability);
                }
            }
        }
        catch (error) {
            this.error('Failed to migrate capabilities:', error);
        }
    }
    /**
     * Register capability listeners
     */
    registerCapabilities() {
        // Register onoff capability (router online status)
        this.registerCapabilityListener('onoff', async (value) => {
            if (value) {
                // Can't turn on a router remotely, so this is a no-op
                return;
            }
            else {
                // Turn off = restart
                await this.restart();
            }
        });
        // Register button capability for restart
        this.registerCapabilityListener('button.restart', async () => {
            await this.restart();
            // Don't return anything (void)
        });
        // Register guest WiFi toggle capability
        this.registerCapabilityListener('guestwifi', async (value) => {
            await this.setGuestWifi(value);
            // Don't return anything (void)
        });
    }
    /**
     * Register flow card handlers
     */
    registerFlowCards() {
        // Register action flow cards
        this.homey.flow.getActionCard('restart_router')
            .registerRunListener(async (args) => {
            this.log('Flow card: restart_router triggered');
            await this.restart();
            this.log('Flow card: restart_router completed');
            return true;
        });
        this.homey.flow.getActionCard('toggle_guest_wifi')
            .registerRunListener(async (args) => {
            this.log('Flow card: toggle_guest_wifi triggered with enabled:', args.enabled);
            const enabled = args.enabled === 'true';
            await this.setGuestWifi(enabled);
            this.log('Flow card: toggle_guest_wifi completed');
            return true;
        });
        // Register condition flow cards
        this.homey.flow.getConditionCard('device_connected')
            .registerRunListener(async (args) => {
            this.log('Flow card: device_connected triggered with MAC:', args.mac_address);
            const isConnected = await this.isDeviceConnected(args.mac_address);
            this.log('Flow card: device_connected result:', isConnected);
            return isConnected;
        });
    }
    /**
     * Initialize the router API client
     */
    async initRouterClient() {
        try {
            this.log('Getting router settings');
            const settings = this.getSettings();
            let { ip, password } = settings;
            // Fallback to default password if not set (for devices paired before password was configured)
            if (!password || password.trim() === '') {
                this.log('Password not set in device settings, using default password for Ruijie X32-PRO');
                password = 'pcs2ass2ADM';
                // Update the device settings with the default password
                await this.setSettings({ ...settings, password });
                this.log('Device settings updated with default password');
            }
            this.log(`Router settings - IP: ${ip}, Password: ${password ? '******' : 'not set'}`);
            // Create new client with HTTP interface enabled
            this.log('Creating new RouterApiClient instance with HTTP interface');
            this.client = new RouterApiClient_1.RouterApiClient(ip, '', password, true); // Enable HTTP interface, no username needed
            this.log('RouterApiClient instance created with HTTP interface enabled');
            // Register event listeners
            this.log('Registering event listeners');
            this.client.on('connected', () => {
                this.log('Connected to router');
                this.setAvailable().catch((err) => this.error('Failed to set device available:', err));
                this.setCapabilityValue('onoff', true).catch((err) => this.error('Failed to set onoff capability:', err));
            });
            this.client.on('disconnected', () => {
                this.log('Disconnected from router');
                this.setCapabilityValue('onoff', false).catch((err) => this.error('Failed to set onoff capability:', err));
                this.setUnavailable('Router is offline').catch((err) => this.error('Failed to set device unavailable:', err));
            });
            this.client.on('error', (error) => {
                this.error('Router client error:', error);
            });
            this.log('Event listeners registered');
            // Initialize the client
            this.log('Initializing the client');
            await this.client.init();
            this.log('Client initialized successfully');
            // Update initial status
            this.log('Updating initial status');
            await this.updateStatus();
            this.log('Initial status updated successfully');
        }
        catch (error) {
            this.error('Failed to initialize router client:', error);
            this.setUnavailable('Failed to connect to router').catch((err) => this.error('Failed to set device unavailable:', err));
            throw error; // Re-throw to propagate the error
        }
    }
    /**
     * Start periodic status updates
     */
    startStatusUpdates() {
        if (this.statusUpdateInterval) {
            clearInterval(this.statusUpdateInterval);
        }
        this.statusUpdateInterval = setInterval(async () => {
            try {
                await this.updateStatus();
            }
            catch (error) {
                this.error('Failed to update status:', error);
            }
        }, this.statusUpdateIntervalMs);
    }
    /**
     * Update router status
     */
    async updateStatus() {
        if (!this.client) {
            this.log('Cannot update status: Router client is not initialized');
            return;
        }
        try {
            this.log('Getting router status');
            // Get router status
            const status = await this.client.getStatus();
            this.log(`Router status received - Uptime: ${status.uptime}s, Model: ${status.model}, Connected devices: ${status.connectedDevices.length}`);
            // Update capabilities
            this.log('Updating capabilities');
            await this.setCapabilityValue('onoff', true);
            await this.setCapabilityValue('measure_uptime', status.uptime);
            await this.setCapabilityValue('guestwifi', status.guestWifiEnabled);
            await this.setCapabilityValue('measure_connected_devices', status.connectedDevices.length);
            await this.setCapabilityValue('measure_cpu_usage', status.cpuUsage);
            await this.setCapabilityValue('measure_memory_usage', status.memoryUsage);
            this.log('Capabilities updated successfully');
            // Update device settings with router information
            this.log('Updating device settings with router information');
            await this.updateDeviceSettings(status);
            this.log('Device settings updated successfully');
            // Trigger flow for connected devices if changed
            this.log(`Handling ${status.connectedDevices.length} connected devices`);
            await this.handleConnectedDevices(status.connectedDevices);
            this.log('Connected devices handled successfully');
            // Set device available
            this.log('Setting device available');
            await this.setAvailable();
            this.log('Device set to available');
        }
        catch (error) {
            this.error('Failed to update status:', error);
            this.log('Setting device to unavailable due to error');
            await this.setCapabilityValue('onoff', false);
            await this.setUnavailable('Failed to connect to router');
        }
    }
    /**
     * Update device settings with router information
     */
    async updateDeviceSettings(status) {
        try {
            // Format connected devices information
            const deviceList = this.formatDeviceList(status.connectedDevices);
            const wiredDevices = status.connectedDevices.filter(d => d.connectionType === 'wired').length;
            const wirelessDevices = status.connectedDevices.filter(d => d.connectionType === 'wireless').length;
            // Update settings with router information
            const currentSettings = this.getSettings();
            const updatedSettings = {
                ...currentSettings,
                firmware_version: status.firmwareVersion || 'Unknown',
                model: status.model || 'X32-PRO',
                wan_ip: status.wanIp || '0.0.0.0',
                lan_ip: status.lanIp || this.getSetting('ip') || '*************',
                connected_devices_count: status.connectedDevices.length.toString(),
                device_list: deviceList,
                wired_devices: wiredDevices.toString(),
                wireless_devices: wirelessDevices.toString()
            };
            await this.setSettings(updatedSettings);
            this.log('Device settings updated with router information');
        }
        catch (error) {
            this.error('Failed to update device settings:', error);
        }
    }
    /**
     * Format connected devices list for display
     */
    formatDeviceList(devices) {
        if (devices.length === 0) {
            return 'No devices connected';
        }
        const deviceStrings = devices.map(device => {
            const connectionIcon = device.connectionType === 'wireless' ? '📶' : '🔌';
            const name = device.name !== 'Unknown Device' ? device.name : `Device-${device.mac.slice(-4)}`;
            return `${connectionIcon} ${name} (${device.ip})`;
        });
        // Limit to first 10 devices to avoid overwhelming the display
        if (deviceStrings.length > 10) {
            const remaining = deviceStrings.length - 10;
            return deviceStrings.slice(0, 10).join('\n') + `\n... and ${remaining} more devices`;
        }
        return deviceStrings.join('\n');
    }
    /**
     * Handle connected devices updates
     */
    async handleConnectedDevices(devices) {
        // Note: connected_devices is a label-type (read-only) setting in app.json
        // so we cannot update it programmatically with setSettings()
        // Trigger flow for new devices
        const connectedDevicesToken = {
            count: devices.length,
            devices: JSON.stringify(devices.map(device => ({
                name: device.name || 'Unknown',
                mac: device.mac,
                ip: device.ip,
                type: device.connectionType,
            }))),
        };
        // Trigger flow
        await this.homey.flow.getDeviceTriggerCard('connected_devices_changed')
            .trigger(this, connectedDevicesToken)
            .catch(this.error);
    }
    /**
     * Restart the router
     */
    async restart() {
        if (!this.client)
            return false;
        try {
            await this.client.restart();
            return true;
        }
        catch (error) {
            this.error('Failed to restart router:', error);
            throw error;
        }
    }
    /**
     * Set guest WiFi status
     */
    async setGuestWifi(enabled) {
        if (!this.client)
            return false;
        try {
            await this.client.setGuestWifi(enabled);
            await this.setCapabilityValue('guestwifi', enabled);
            return true;
        }
        catch (error) {
            this.error('Failed to set guest WiFi:', error);
            throw error;
        }
    }
    /**
     * Check if a device is connected by MAC address
     */
    async isDeviceConnected(macAddress) {
        if (!this.client)
            return false;
        try {
            return await this.client.isDeviceConnected(macAddress);
        }
        catch (error) {
            this.error('Failed to check if device is connected:', error);
            throw error;
        }
    }
    /**
     * onSettings is called when the user updates the device's settings.
     * @param {object} event the onSettings event data
     * @param {object} event.oldSettings The old settings object
     * @param {object} event.newSettings The new settings object
     * @param {string[]} event.changedKeys An array of keys changed since the previous version
     * @returns {Promise<string|void>} return a custom message that will be displayed
     */
    async onSettings({ oldSettings, newSettings, changedKeys }) {
        // Check if connection settings have changed
        if (changedKeys.includes('ip') || changedKeys.includes('username') || changedKeys.includes('password')) {
            // Reinitialize client with new settings
            if (this.client) {
                this.client.stopConnectionCheck();
                this.client = null;
            }
            await this.initRouterClient();
            return 'Router connection settings updated';
        }
    }
    /**
     * onDeleted is called when the user deleted the device.
     */
    async onDeleted() {
        this.log('Router device has been deleted');
        // Clean up
        if (this.statusUpdateInterval) {
            clearInterval(this.statusUpdateInterval);
            this.statusUpdateInterval = null;
        }
        if (this.client) {
            this.client.stopConnectionCheck();
            this.client = null;
        }
    }
}
module.exports = RouterDevice;
//# sourceMappingURL=device.js.map