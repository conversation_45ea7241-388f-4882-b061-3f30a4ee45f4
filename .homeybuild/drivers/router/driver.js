"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const homey_1 = __importDefault(require("homey"));
class RouterDriver extends homey_1.default.Driver {
    constructor() {
        super(...arguments);
        this.validatedDevice = null;
    }
    /**
     * onInit is called when the driver is initialized.
     */
    async onInit() {
        this.log('Router driver has been initialized');
    }
    /**
     * Simple network discovery to find potential routers
     */
    async discoverRouters() {
        this.log('Starting router discovery');
        const potentialRouters = [
            { ip: '*************', name: 'Ruijie X32-PRO Router (*************)' },
            { ip: '***********', name: 'Ruijie X32-PRO Router (***********)' },
            { ip: '***********', name: 'Ruijie X32-PRO Router (***********)' },
        ];
        const discoveredRouters = [];
        for (const router of potentialRouters) {
            try {
                this.log(`Testing connection to ${router.ip}`);
                // Try to connect to the router's web interface
                const axios = require('axios');
                const response = await axios.get(`http://${router.ip}/cgi-bin/luci/`, {
                    timeout: 3000,
                    validateStatus: () => true // Accept any status code
                });
                if (response.status === 200 && response.data.includes('X32-PRO')) {
                    this.log(`Found Ruijie X32-PRO router at ${router.ip}`);
                    discoveredRouters.push({
                        name: router.name,
                        data: {
                            id: `router-${router.ip.replace(/\./g, '-')}`,
                        },
                        settings: {
                            ip: router.ip,
                            password: 'pcs2ass2ADM', // Default password for Ruijie X32-PRO
                        },
                    });
                }
            }
            catch (error) {
                this.log(`No router found at ${router.ip}: ${error instanceof Error ? error.message : 'Unknown error'}`);
            }
        }
        this.log(`Discovery completed. Found ${discoveredRouters.length} routers`);
        return discoveredRouters;
    }
    /**
     * Handle the pairing process
     */
    async onPair(session) {
        this.log('onPair called, setting up pairing session');
        // Clear any previous validated device
        this.validatedDevice = null;
        // Register listener for when the user submits router credentials
        session.setHandler('validate_router_credentials', async (data) => {
            try {
                const { ip, password } = data;
                this.log(`Validating router credentials - IP: ${ip}, Password: ${password ? '******' : 'not set'}`);
                // Try to connect to the router to validate credentials
                this.log('Importing RouterApiClient');
                const { RouterApiClient } = await Promise.resolve().then(() => __importStar(require('../../lib/RouterApiClient')));
                this.log('Creating new RouterApiClient instance');
                const client = new RouterApiClient(ip, '', password); // No username needed
                this.log('Initializing client to test connection');
                await client.init();
                // If we get here, the connection was successful
                this.log('Router connection validated successfully');
                // Create device data for the list_devices view
                const deviceId = `router-${ip.replace(/\./g, '-')}`;
                this.validatedDevice = {
                    name: `Ruijie X32-PRO Router (${ip})`,
                    data: {
                        id: deviceId,
                    },
                    settings: {
                        ip,
                        password,
                    },
                };
                this.log('Stored validated device data:', JSON.stringify(this.validatedDevice, null, 2));
                // Clean up the client
                client.stopConnectionCheck();
                return true;
            }
            catch (error) {
                this.error('Failed to validate router credentials:', error);
                return false;
            }
        });
        // Handle the list_devices step
        session.setHandler('list_devices', async () => {
            this.log('list_devices handler called');
            if (this.validatedDevice) {
                this.log('Returning validated device:', JSON.stringify(this.validatedDevice, null, 2));
                return [this.validatedDevice];
            }
            // If no validated device, try to discover routers on the network
            this.log('No validated device found, attempting network discovery');
            try {
                const discoveredRouters = await this.discoverRouters();
                if (discoveredRouters.length > 0) {
                    this.log(`Network discovery found ${discoveredRouters.length} routers:`, JSON.stringify(discoveredRouters, null, 2));
                    return discoveredRouters;
                }
                else {
                    this.log('Network discovery completed but no routers found');
                }
            }
            catch (error) {
                this.error('Network discovery failed:', error);
            }
            this.log('No routers available for pairing, returning empty array');
            return [];
        });
        // Clean up session when pairing is done or cancelled
        session.setHandler('disconnect', async () => {
            this.log('Cleaning up pairing session');
            this.validatedDevice = null;
        });
    }
    /**
     * onPairListDevices is called when a user is adding a device and the 'list_devices' view is called.
     * This should return an array with the data of devices that are available for pairing.
     */
    async onPairListDevices() {
        this.log('onPairListDevices called');
        // Check if we have stored device data from the credentials step
        if (this.validatedDevice) {
            this.log('Returning stored device data:', this.validatedDevice);
            return [this.validatedDevice];
        }
        // If no pending device, try to discover routers on the network
        this.log('No pending device, attempting network discovery');
        try {
            const discoveredRouters = await this.discoverRouters();
            if (discoveredRouters.length > 0) {
                this.log(`Returning ${discoveredRouters.length} discovered routers`);
                return discoveredRouters;
            }
        }
        catch (error) {
            this.error('Network discovery failed:', error);
        }
        this.log('No routers found, returning empty array');
        return [];
    }
}
module.exports = RouterDriver;
//# sourceMappingURL=driver.js.map