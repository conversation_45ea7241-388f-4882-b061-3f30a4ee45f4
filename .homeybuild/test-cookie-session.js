const { RouterApiClient } = require('./build/lib/RouterApiClient');

const ROUTER_CONFIG = {
  ip: '*************',
  password: 'pcs2ass2ADM'
};

async function testCookieSession() {
  console.log('=== Testing Cookie Session Management ===\n');
  
  try {
    // Create RouterApiClient instance
    console.log('Creating RouterApiClient instance...');
    const client = new RouterApiClient(ROUTER_CONFIG.ip, '', ROUTER_CONFIG.password);
    
    // Test 1: Initial login
    console.log('\n--- Test 1: Initial Login ---');
    await client.init();
    console.log('✓ Initial login successful');
    
    // Test 2: Get router status (should use existing session)
    console.log('\n--- Test 2: Get Router Status ---');
    const status = await client.getStatus();
    console.log('✓ Router status retrieved:', {
      model: status.model,
      firmwareVersion: status.firmwareVersion,
      connectedDevices: status.connectedDevices.length,
      uptime: status.uptime
    });
    
    // Test 3: Get connected devices (the main test for session persistence)
    console.log('\n--- Test 3: Get Connected Devices ---');
    const devices = await client.getConnectedDevices();
    console.log(`✓ Found ${devices.length} connected devices`);
    
    if (devices.length > 0) {
      console.log('Sample devices:');
      devices.slice(0, 3).forEach((device, index) => {
        console.log(`  ${index + 1}. ${device.name} (${device.ip}) - ${device.connectionType}`);
      });
    }
    
    // Test 4: Multiple consecutive requests to test session persistence
    console.log('\n--- Test 4: Multiple Consecutive Requests ---');
    for (let i = 1; i <= 3; i++) {
      console.log(`Request ${i}:`);
      const testDevices = await client.getConnectedDevices();
      console.log(`  ✓ Found ${testDevices.length} devices`);
      
      // Small delay between requests
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    console.log('\n✓ All tests completed successfully!');
    console.log('✓ Cookie session management appears to be working correctly');
    
    // Clean up
    client.stopConnectionCheck();
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    console.error('Full error:', error);
  }
}

// Run the test
testCookieSession().catch(console.error);
