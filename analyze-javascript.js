const axios = require('axios');

const ROUTER_CONFIG = {
  ip: '*************',
  password: 'pcs2ass2ADM'
};

async function analyzeJavaScript() {
  console.log('=== Analyzing JavaScript Files for API Calls ===\n');
  
  try {
    // Step 1: Login and get session
    console.log('Step 1: Login');
    const loginRes = await axios.post(`http://${ROUTER_CONFIG.ip}/cgi-bin/luci/api/auth`, {
      method: "login",
      params: {
        password: ROUTER_CONFIG.password,
        time: Math.floor(Date.now() / 1000).toString(),
        encry: false,
        limit: false
      }
    }, {
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0',
      }
    });

    if (!loginRes.data?.data?.token) {
      console.log('❌ Login failed');
      return;
    }

    const token = loginRes.data.data.token;
    console.log(`✓ Login successful - Token: ${token.substring(0, 8)}...`);

    // Step 2: Get the main page to extract JavaScript file URLs
    console.log('\nStep 2: Getting JavaScript file URLs');
    
    let sessionCookie = '';
    if (loginRes.headers['set-cookie']) {
      const cookies = loginRes.headers['set-cookie'];
      for (const cookie of cookies) {
        const [nameValue] = cookie.split(';');
        sessionCookie = nameValue;
        break;
      }
    }

    const mainPageRes = await axios.get(`http://${ROUTER_CONFIG.ip}/cgi-bin/luci/;stok=${token}/ehr/home_user`, {
      headers: {
        'User-Agent': 'Mozilla/5.0',
        'Cookie': sessionCookie
      }
    });

    // Extract JavaScript file URLs from the HTML
    const jsFileMatches = mainPageRes.data.match(/src="([^"]*\.js[^"]*)"/g) || [];
    const jsFiles = jsFileMatches.map(match => {
      const url = match.match(/src="([^"]*)"/)[1];
      return url.startsWith('/') ? `http://${ROUTER_CONFIG.ip}${url}` : url;
    });

    console.log(`Found ${jsFiles.length} JavaScript files:`);
    jsFiles.forEach(file => console.log(`  ${file}`));

    // Step 3: Download and analyze JavaScript files for API patterns
    console.log('\nStep 3: Analyzing JavaScript files for API patterns');
    
    for (const jsFile of jsFiles) {
      try {
        console.log(`\nAnalyzing: ${jsFile}`);
        const jsRes = await axios.get(jsFile, {
          headers: {
            'User-Agent': 'Mozilla/5.0',
            'Accept': 'application/javascript, */*'
          },
          timeout: 10000
        });

        const jsContent = jsRes.data;
        console.log(`  Size: ${jsContent.length} bytes`);

        // Look for API endpoint patterns
        const apiPatterns = [
          // Common API patterns
          /['"`]\/[^'"`]*api[^'"`]*['"`]/gi,
          /['"`]\/[^'"`]*rpc[^'"`]*['"`]/gi,
          /['"`]\/[^'"`]*data[^'"`]*['"`]/gi,
          /['"`]\/[^'"`]*client[^'"`]*['"`]/gi,
          /['"`]\/[^'"`]*device[^'"`]*['"`]/gi,
          /['"`]\/[^'"`]*dhcp[^'"`]*['"`]/gi,
          /['"`]\/[^'"`]*wireless[^'"`]*['"`]/gi,
          /['"`]\/[^'"`]*network[^'"`]*['"`]/gi,
          /['"`]\/[^'"`]*status[^'"`]*['"`]/gi,
          /['"`]\/[^'"`]*home[^'"`]*['"`]/gi,
          /['"`]\/[^'"`]*ehr[^'"`]*['"`]/gi,
          
          // Method patterns
          /method\s*:\s*['"`][^'"`]*['"`]/gi,
          /action\s*:\s*['"`][^'"`]*['"`]/gi,
          
          // URL building patterns
          /stok\s*\+[^;]*/gi,
          /token\s*\+[^;]*/gi,
        ];

        const foundPatterns = new Set();
        
        for (const pattern of apiPatterns) {
          const matches = jsContent.match(pattern) || [];
          matches.forEach(match => foundPatterns.add(match));
        }

        if (foundPatterns.size > 0) {
          console.log(`  Found ${foundPatterns.size} API patterns:`);
          Array.from(foundPatterns).slice(0, 20).forEach(pattern => {
            console.log(`    ${pattern}`);
          });
        }

        // Look for specific device-related function calls
        const devicePatterns = [
          /get[A-Z][a-zA-Z]*[Cc]lient[s]?/g,
          /get[A-Z][a-zA-Z]*[Dd]evice[s]?/g,
          /get[A-Z][a-zA-Z]*[Dd]hcp/g,
          /get[A-Z][a-zA-Z]*[Ww]ireless/g,
          /fetch[A-Z][a-zA-Z]*[Cc]lient[s]?/g,
          /fetch[A-Z][a-zA-Z]*[Dd]evice[s]?/g,
          /load[A-Z][a-zA-Z]*[Cc]lient[s]?/g,
          /load[A-Z][a-zA-Z]*[Dd]evice[s]?/g,
        ];

        const deviceFunctions = new Set();
        for (const pattern of devicePatterns) {
          const matches = jsContent.match(pattern) || [];
          matches.forEach(match => deviceFunctions.add(match));
        }

        if (deviceFunctions.size > 0) {
          console.log(`  Found ${deviceFunctions.size} device-related functions:`);
          Array.from(deviceFunctions).forEach(func => {
            console.log(`    ${func}`);
          });
        }

      } catch (error) {
        console.log(`  ❌ Error analyzing ${jsFile}: ${error.message}`);
      }
    }

    // Step 4: Try the original auth API with different methods
    console.log('\nStep 4: Testing auth API with different methods');
    
    const authMethods = [
      { method: 'get_clients' },
      { method: 'get_devices' },
      { method: 'get_dhcp_leases' },
      { method: 'get_wireless_clients' },
      { method: 'get_network_status' },
      { method: 'get_system_info' },
      { method: 'get_home_data' },
      { method: 'list_clients' },
      { method: 'dhcp_clients' },
      { method: 'wireless_clients' },
      { method: 'network_clients' },
      { method: 'client_list' },
      { method: 'device_list' },
      { method: 'home_user_list' },
      { method: 'ehr_clients' },
      { method: 'ehr_devices' },
    ];

    for (const authMethod of authMethods) {
      try {
        const response = await axios.post(`http://${ROUTER_CONFIG.ip}/cgi-bin/luci/api/auth`, authMethod, {
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0',
            'Cookie': sessionCookie,
            'Accept': 'application/json, text/plain, */*',
            'X-Requested-With': 'XMLHttpRequest'
          },
          timeout: 5000
        });

        console.log(`✓ Auth API ${authMethod.method}`);
        console.log(`  Status: ${response.status}`);
        console.log(`  Content-Type: ${response.headers['content-type'] || 'unknown'}`);
        
        if (response.headers['content-type']?.includes('json') || 
            (typeof response.data === 'object' && response.data !== null)) {
          console.log(`  🎯 JSON Response: ${JSON.stringify(response.data).substring(0, 300)}...`);
          
          // Check for device data
          const dataStr = JSON.stringify(response.data).toLowerCase();
          if (dataStr.includes('mac') || dataStr.includes('ip') || dataStr.includes('client') || 
              dataStr.includes('device') || dataStr.includes('dhcp') || dataStr.includes('192.168')) {
            console.log(`  🔥 CONTAINS DEVICE DATA!`);
          }
        }
        console.log('');
        
      } catch (error) {
        if (error.response && error.response.status !== 404) {
          console.log(`❌ Auth API ${authMethod.method} - Status: ${error.response.status}`);
        }
      }
    }

  } catch (error) {
    console.error('❌ JavaScript analysis failed:', error.message);
  }
}

analyzeJavaScript().catch(console.error);
