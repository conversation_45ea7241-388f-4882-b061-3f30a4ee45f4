const axios = require('axios');

const ROUTER_CONFIG = {
  ip: '*************',
  password: 'pcs2ass2ADM'
};

async function testAuthMethods() {
  console.log('=== Testing Auth API Methods for Device Data ===\n');
  
  try {
    // Step 1: Login and get session
    console.log('Step 1: Login');
    const loginRes = await axios.post(`http://${ROUTER_CONFIG.ip}/cgi-bin/luci/api/auth`, {
      method: "login",
      params: {
        password: ROUTER_CONFIG.password,
        time: Math.floor(Date.now() / 1000).toString(),
        encry: false,
        limit: false
      }
    }, {
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0',
      }
    });

    if (!loginRes.data?.data?.token) {
      console.log('❌ Login failed');
      return;
    }

    const token = loginRes.data.data.token;
    console.log(`✓ Login successful - Token: ${token.substring(0, 8)}...`);

    // Step 2: Test auth API with comprehensive method list
    console.log('\nStep 2: Testing auth API with comprehensive method list\n');
    
    const authMethods = [
      // System info methods
      'get_system_info',
      'get_status',
      'get_info',
      'system_info',
      'status',
      'info',
      
      // Network methods
      'get_network_status',
      'get_network_info',
      'network_status',
      'network_info',
      'network',
      
      // Client/Device methods
      'get_clients',
      'get_devices', 
      'get_client_list',
      'get_device_list',
      'get_connected_devices',
      'get_dhcp_clients',
      'get_dhcp_leases',
      'get_wireless_clients',
      'get_lan_clients',
      'clients',
      'devices',
      'client_list',
      'device_list',
      'connected_devices',
      'dhcp_clients',
      'dhcp_leases',
      'wireless_clients',
      'lan_clients',
      
      // Home/Overview methods
      'get_home_data',
      'get_overview',
      'get_dashboard',
      'home_data',
      'overview',
      'dashboard',
      
      // Wireless methods
      'get_wireless_status',
      'get_wireless_info',
      'wireless_status',
      'wireless_info',
      'wireless',
      
      // DHCP methods
      'get_dhcp_status',
      'get_dhcp_info',
      'dhcp_status',
      'dhcp_info',
      'dhcp',
      
      // Interface methods
      'get_interfaces',
      'get_interface_status',
      'interfaces',
      'interface_status',
      
      // Statistics methods
      'get_statistics',
      'get_stats',
      'statistics',
      'stats',
      
      // Ruijie specific methods
      'ehr_get_clients',
      'ehr_get_devices',
      'ehr_clients',
      'ehr_devices',
      'ehr_status',
      'ehr_info',
      
      // Common router methods
      'show_clients',
      'show_devices',
      'list_clients',
      'list_devices',
      'scan_clients',
      'scan_devices',
      
      // Alternative formats
      'getClients',
      'getDevices',
      'getClientList',
      'getDeviceList',
      'getConnectedDevices',
      'getDhcpClients',
      'getWirelessClients',
      
      // Command-style methods
      'cmd_get_clients',
      'cmd_show_clients',
      'cmd_list_clients',
      'exec_get_clients',
      'run_get_clients',
    ];

    for (const method of authMethods) {
      try {
        const response = await axios.post(`http://${ROUTER_CONFIG.ip}/cgi-bin/luci/api/auth`, {
          method: method
        }, {
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0',
          },
          timeout: 5000
        });

        console.log(`✓ Method: ${method}`);
        console.log(`  Status: ${response.status}`);
        
        if (response.data) {
          const responseStr = JSON.stringify(response.data);
          console.log(`  Response: ${responseStr.substring(0, 200)}${responseStr.length > 200 ? '...' : ''}`);
          
          // Check if we got a successful response (not "Method not found")
          if (response.data.error && response.data.error.message === "Method not found.") {
            // Skip - method not found
          } else if (response.data.data !== null && response.data.data !== undefined) {
            console.log(`  🎯 SUCCESS - Got data!`);
            console.log(`  Full response: ${JSON.stringify(response.data, null, 2)}`);
          }
          
          // Check for device-related content
          const dataStr = responseStr.toLowerCase();
          if (dataStr.includes('mac') || dataStr.includes('ip') || dataStr.includes('client') || 
              dataStr.includes('device') || dataStr.includes('dhcp') || dataStr.includes('192.168')) {
            console.log(`  🔥 CONTAINS DEVICE DATA!`);
          }
        }
        console.log('');
        
      } catch (error) {
        if (error.response && error.response.status !== 404) {
          console.log(`❌ Method ${method} - Status: ${error.response.status}`);
        }
      }
    }

    // Step 3: Test with parameters
    console.log('\nStep 3: Testing methods with parameters\n');
    
    const methodsWithParams = [
      { method: 'get_clients', params: {} },
      { method: 'get_devices', params: {} },
      { method: 'clients', params: {} },
      { method: 'devices', params: {} },
      { method: 'get_status', params: {} },
      { method: 'status', params: {} },
      { method: 'get_info', params: {} },
      { method: 'info', params: {} },
      { method: 'get_clients', params: { type: 'all' } },
      { method: 'get_devices', params: { type: 'connected' } },
      { method: 'get_clients', params: { interface: 'lan' } },
      { method: 'get_clients', params: { interface: 'wireless' } },
    ];

    for (const { method, params } of methodsWithParams) {
      try {
        const response = await axios.post(`http://${ROUTER_CONFIG.ip}/cgi-bin/luci/api/auth`, {
          method: method,
          params: params
        }, {
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0',
          },
          timeout: 5000
        });

        console.log(`✓ Method: ${method} with params: ${JSON.stringify(params)}`);
        
        if (response.data) {
          const responseStr = JSON.stringify(response.data);
          console.log(`  Response: ${responseStr.substring(0, 200)}${responseStr.length > 200 ? '...' : ''}`);
          
          if (response.data.error && response.data.error.message === "Method not found.") {
            // Skip
          } else if (response.data.data !== null && response.data.data !== undefined) {
            console.log(`  🎯 SUCCESS - Got data!`);
            console.log(`  Full response: ${JSON.stringify(response.data, null, 2)}`);
          }
          
          const dataStr = responseStr.toLowerCase();
          if (dataStr.includes('mac') || dataStr.includes('ip') || dataStr.includes('client') || 
              dataStr.includes('device') || dataStr.includes('dhcp') || dataStr.includes('192.168')) {
            console.log(`  🔥 CONTAINS DEVICE DATA!`);
          }
        }
        console.log('');
        
      } catch (error) {
        if (error.response && error.response.status !== 404) {
          console.log(`❌ Method ${method} with params - Status: ${error.response.status}`);
        }
      }
    }

  } catch (error) {
    console.error('❌ Auth method test failed:', error.message);
  }
}

testAuthMethods().catch(console.error);
