const axios = require('axios');

const ROUTER_CONFIG = {
  ip: '*************',
  password: 'pcs2ass2ADM'
};

async function findApiEndpoints() {
  console.log('=== Finding Real API Endpoints ===\n');
  
  try {
    // Step 1: Login and get session
    console.log('Step 1: Login');
    const loginRes = await axios.post(`http://${ROUTER_CONFIG.ip}/cgi-bin/luci/api/auth`, {
      method: "login",
      params: {
        password: ROUTER_CONFIG.password,
        time: Math.floor(Date.now() / 1000).toString(),
        encry: false,
        limit: false
      }
    }, {
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0',
      }
    });

    if (!loginRes.data?.data?.token) {
      console.log('❌ Login failed');
      return;
    }

    const token = loginRes.data.data.token;
    
    // Extract session cookie
    let sessionCookie = '';
    if (loginRes.headers['set-cookie']) {
      const cookies = loginRes.headers['set-cookie'];
      for (const cookie of cookies) {
        const [nameValue] = cookie.split(';');
        sessionCookie = nameValue;
        break;
      }
    }

    console.log(`✓ Login successful - Token: ${token.substring(0, 8)}...`);

    // Step 2: Try common API endpoints that SPAs use for device data
    const apiEndpoints = [
      // Common device/client endpoints
      `/cgi-bin/luci/;stok=${token}/api/clients`,
      `/cgi-bin/luci/;stok=${token}/api/devices`,
      `/cgi-bin/luci/;stok=${token}/api/dhcp`,
      `/cgi-bin/luci/;stok=${token}/api/wireless`,
      `/cgi-bin/luci/;stok=${token}/api/network`,
      `/cgi-bin/luci/;stok=${token}/api/status`,
      `/cgi-bin/luci/;stok=${token}/api/home`,
      `/cgi-bin/luci/;stok=${token}/api/overview`,
      
      // Ruijie specific endpoints
      `/cgi-bin/luci/;stok=${token}/api/ehr/clients`,
      `/cgi-bin/luci/;stok=${token}/api/ehr/devices`,
      `/cgi-bin/luci/;stok=${token}/api/ehr/home`,
      `/cgi-bin/luci/;stok=${token}/api/ehr/users`,
      
      // RPC style endpoints
      `/cgi-bin/luci/;stok=${token}/rpc/clients`,
      `/cgi-bin/luci/;stok=${token}/rpc/devices`,
      `/cgi-bin/luci/;stok=${token}/rpc/dhcp`,
      `/cgi-bin/luci/;stok=${token}/rpc/wireless`,
      
      // Data endpoints
      `/cgi-bin/luci/;stok=${token}/data/clients`,
      `/cgi-bin/luci/;stok=${token}/data/devices`,
      `/cgi-bin/luci/;stok=${token}/data/dhcp`,
      
      // Admin endpoints that might return JSON
      `/cgi-bin/luci/;stok=${token}/admin/network/dhcp/leases`,
      `/cgi-bin/luci/;stok=${token}/admin/status/overview/data`,
      `/cgi-bin/luci/;stok=${token}/admin/network/wireless/status`,
    ];

    console.log(`\nStep 2: Testing ${apiEndpoints.length} potential API endpoints\n`);

    for (const endpoint of apiEndpoints) {
      try {
        const response = await axios.get(`http://${ROUTER_CONFIG.ip}${endpoint}`, {
          headers: {
            'User-Agent': 'Mozilla/5.0',
            'Cookie': sessionCookie,
            'Accept': 'application/json, text/plain, */*',
            'X-Requested-With': 'XMLHttpRequest'
          },
          timeout: 5000
        });

        console.log(`✓ ${endpoint}`);
        console.log(`  Status: ${response.status}`);
        console.log(`  Content-Type: ${response.headers['content-type'] || 'unknown'}`);
        console.log(`  Length: ${response.data?.length || 0} bytes`);
        
        // Check if response looks like JSON
        if (response.headers['content-type']?.includes('json') || 
            (typeof response.data === 'object') ||
            (typeof response.data === 'string' && response.data.trim().startsWith('{'))) {
          console.log(`  🎯 JSON Response: ${JSON.stringify(response.data).substring(0, 200)}...`);
        } else if (response.data && response.data.length < 1000) {
          console.log(`  📄 Text Response: ${response.data.substring(0, 200)}...`);
        }
        
        // Check for device-related content
        const dataStr = JSON.stringify(response.data).toLowerCase();
        if (dataStr.includes('mac') || dataStr.includes('ip') || dataStr.includes('client') || 
            dataStr.includes('device') || dataStr.includes('dhcp') || dataStr.includes('192.168')) {
          console.log(`  🔥 CONTAINS DEVICE DATA!`);
        }
        
        console.log('');
        
      } catch (error) {
        if (error.response) {
          console.log(`❌ ${endpoint} - Status: ${error.response.status}`);
        } else {
          console.log(`❌ ${endpoint} - Error: ${error.message}`);
        }
      }
    }

    // Step 3: Try POST requests with common RPC methods
    console.log('\nStep 3: Testing RPC-style POST requests\n');
    
    const rpcMethods = [
      { method: 'get_clients' },
      { method: 'get_devices' },
      { method: 'get_dhcp_clients' },
      { method: 'get_wireless_clients' },
      { method: 'get_network_status' },
      { method: 'get_home_data' },
      { method: 'list_clients' },
      { method: 'list_devices' },
      { method: 'dhcp_leases' },
      { method: 'wireless_status' },
    ];

    for (const rpcMethod of rpcMethods) {
      try {
        const response = await axios.post(`http://${ROUTER_CONFIG.ip}/cgi-bin/luci/;stok=${token}/api/rpc`, rpcMethod, {
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0',
            'Cookie': sessionCookie,
            'Accept': 'application/json, text/plain, */*',
            'X-Requested-With': 'XMLHttpRequest'
          },
          timeout: 5000
        });

        console.log(`✓ RPC ${rpcMethod.method}`);
        console.log(`  Status: ${response.status}`);
        console.log(`  Response: ${JSON.stringify(response.data).substring(0, 200)}...`);
        
        // Check for device-related content
        const dataStr = JSON.stringify(response.data).toLowerCase();
        if (dataStr.includes('mac') || dataStr.includes('ip') || dataStr.includes('client') || 
            dataStr.includes('device') || dataStr.includes('dhcp') || dataStr.includes('192.168')) {
          console.log(`  🔥 CONTAINS DEVICE DATA!`);
        }
        console.log('');
        
      } catch (error) {
        if (error.response) {
          console.log(`❌ RPC ${rpcMethod.method} - Status: ${error.response.status}`);
        } else {
          console.log(`❌ RPC ${rpcMethod.method} - Error: ${error.message}`);
        }
      }
    }

  } catch (error) {
    console.error('❌ API endpoint discovery failed:', error.message);
  }
}

findApiEndpoints().catch(console.error);
