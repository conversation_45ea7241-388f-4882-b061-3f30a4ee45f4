{"name": "com.ruijie.x32pro", "version": "1.0.0", "main": "build/app.js", "scripts": {"build": "tsc", "lint": "eslint --ext .ts,.js --ignore-path .gitignore ."}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/homey": "npm:homey-apps-sdk-v3-types@^0.3.4", "@types/node": "^18.15.11", "@types/tough-cookie": "^4.0.5", "eslint": "^7.32.0", "eslint-config-athom": "^3.1.1", "typescript": "^4.9.5"}, "dependencies": {"axios": "^1.4.0", "crypto-js": "^4.2.0", "source-map-support": "^0.5.21", "tough-cookie": "^5.1.2"}}